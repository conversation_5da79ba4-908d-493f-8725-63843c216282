[{"rule": "Column count in uploaded file should match template", "source_procedure": "SP_VALIDATE_FILE_STRUCTURE", "columns": [], "type": "column_count_check"}, {"rule": "All required columns must be present in the 'FundHoldings' table.", "source_procedure": "SP_VALIDATE_MANDATORY_COLUMNS", "columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "type": "column_presence_check"}, {"rule": "All columns must be present", "source_procedure": "SP_CLEAN_DATA", "columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "type": "column_presence_check"}, {"rule": "Column count in uploaded file should match template", "source_procedure": "SP_CLEAN_DATA", "columns": [], "type": "column_count_check"}, {"rule": "Data types should match the template", "source_procedure": "SP_CLEAN_DATA", "columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "type": "datatype_check"}, {"rule": "Column count in uploaded file should match template", "source_procedure": "SP_FINAL_VALIDATION", "columns": [], "type": "column_count_check"}, {"rule": "Missing required columns", "source_procedure": "SP_FINAL_VALIDATION", "columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "type": "column_presence_check"}, {"rule": "Data type of columns should match the template", "source_procedure": "SP_FINAL_VALIDATION", "columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "type": "datatype_check"}]