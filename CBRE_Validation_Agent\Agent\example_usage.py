"""
Example usage of the LLM Data Validation Agent

This script demonstrates how to use the LLM Data Validation Agent
to validate Excel files using AI-powered validation rules.
"""

import os
import sys
from llm_data_validation_agent import LLMDataValidationAgent


def validate_excel_file(file_path: str):
    """
    Validate an Excel file using the LLM Data Validation Agent
    
    Args:
        file_path: Path to the Excel file to validate
    """
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    # Check if validation rules exist
    rules_path = "../store_procedure/data_validation.json"
    if not os.path.exists(rules_path):
        print("❌ Validation rules not found!")
        print("Please run extract_rules_agent.py first to generate validation rules.")
        return
    
    try:
        # Initialize the validation agent
        print("🤖 Initializing LLM Data Validation Agent...")
        agent = LLMDataValidationAgent(data_validation_path=rules_path)
        
        # Generate output file name
        base_name = os.path.splitext(file_path)[0]
        output_file = f"{base_name}_validated.xlsx"
        
        print(f"📖 Input file: {file_path}")
        print(f"💾 Output file: {output_file}")
        print()
        
        # Validate the file
        validated_df = agent.validate_file(file_path, output_file)
        
        # Display summary
        summary = agent.get_validation_summary(validated_df)
        
        print("\n" + "="*60)
        print("📊 VALIDATION SUMMARY")
        print("="*60)
        print(f"📋 Total rows processed: {summary['total_rows']}")
        print(f"✅ Rows passed validation: {summary['correct_rows']}")
        print(f"❌ Rows failed validation: {summary['incorrect_rows']}")
        print(f"📈 Success rate: {summary['success_rate']}%")
        
        if summary['error_breakdown']:
            print("\n🔍 Common validation errors:")
            for error, count in summary['error_breakdown'].items():
                print(f"   • {error}: {count} occurrence(s)")
        
        print(f"\n💾 Validated file saved as: {output_file}")
        print("\nThe output file contains your original data plus two new columns:")
        print("   • Is_correct: True/False for each row")
        print("   • Why: Explanation of validation result")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")


def interactive_mode():
    """Interactive mode for file validation"""
    
    print("🚀 LLM Data Validation Agent - Interactive Mode")
    print("="*60)
    print()
    
    while True:
        print("Options:")
        print("1. Validate an Excel file")
        print("2. Exit")
        
        choice = input("\nEnter your choice (1-2): ").strip()
        
        if choice == "1":
            file_path = input("\nEnter the path to your Excel file: ").strip()
            if file_path:
                print()
                validate_excel_file(file_path)
            else:
                print("❌ Please provide a valid file path")
        
        elif choice == "2":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please enter 1 or 2.")
        
        print("\n" + "-"*60 + "\n")


def main():
    """Main function"""
    
    # Check command line arguments
    if len(sys.argv) > 1:
        # File path provided as command line argument
        file_path = sys.argv[1]
        validate_excel_file(file_path)
    else:
        # Interactive mode
        interactive_mode()


if __name__ == "__main__":
    main()
