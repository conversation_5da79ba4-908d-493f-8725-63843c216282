"""
Quick test to verify the LLM Data Validation Agent is working
"""

import pandas as pd
import os
from llm_data_validation_agent import LLMDataValidationAgent


def create_simple_test_data():
    """Create a simple test Excel file"""
    
    test_data = {
        'UNIQUE_ID': ['FUND001', 'TOTAL_SUMMARY', 'FUND003'],
        'PORTFOLIO_ID': ['PORT001', 'PORT002', 'PORT003'],
        'REGISTERED_HOLDER': ['Holder A', 'Holder B', 'Holder C'],
        'NAV': [1000.50, 0, 2500.75],
        'OWNERSHIP_PERCENTAGE': [25.5, 0, 15.75],
        'CAPITAL_CALLED': [500000, 0, 750000],
        'NO_OF_SHARES': [1000, 0, 1500],
        'COMMITTED_CAPITAL': [1000000, 0, 1500000],
        'PERIOD': ['2024-Q1', '2024-Q1', '2024-Q1'],
        'FUND_NAME': ['Fund Alpha', 'Fund Beta', 'Fund Gamma']
    }
    
    df = pd.DataFrame(test_data)
    test_file = "quick_test_data.xlsx"
    df.to_excel(test_file, index=False)
    print(f"✅ Created test file: {test_file}")
    return test_file


def main():
    print("🧪 Quick Test - LLM Data Validation Agent")
    print("=" * 50)
    
    try:
        # Test 1: Initialize agent
        print("1️⃣ Testing agent initialization...")
        agent = LLMDataValidationAgent()
        print("✅ Agent initialized successfully!")
        
        # Test 2: Check validation rules
        print(f"\n2️⃣ Checking validation rules...")
        print(f"✅ Loaded {len(agent.validation_rules)} validation rules")
        
        # Display first few rules
        print("\n📋 Sample validation rules:")
        for i, rule in enumerate(agent.validation_rules[:3], 1):
            print(f"   {i}. {rule.get('rule', 'N/A')}")
        
        # Test 3: Create and validate test data
        print(f"\n3️⃣ Creating test data...")
        test_file = create_simple_test_data()
        
        print(f"\n4️⃣ Running validation (this may take a moment)...")
        output_file = "quick_test_validated.xlsx"
        
        # Validate just the first row to test the API connection
        df = pd.read_excel(test_file)
        print(f"📊 Test data has {len(df)} rows")
        
        # Test with just first row
        print("🔍 Testing validation on first row...")
        df_small = df.head(1).copy()
        df_small.to_excel("single_row_test.xlsx", index=False)
        
        validated_df = agent.validate_file("single_row_test.xlsx", "single_row_validated.xlsx")
        
        print("✅ Validation completed!")
        
        # Show results
        print("\n📋 Validation Results:")
        print(validated_df[['UNIQUE_ID', 'NAV', 'Is_correct', 'Why']].to_string(index=False))
        
        # Clean up
        for file in ["quick_test_data.xlsx", "single_row_test.xlsx", "single_row_validated.xlsx"]:
            if os.path.exists(file):
                os.remove(file)
        
        print("\n🎉 Quick test completed successfully!")
        print("The LLM Data Validation Agent is working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check if your OpenAI API key is valid")
        print("2. Ensure you have internet connectivity")
        print("3. Verify the data_validation.json file exists")


if __name__ == "__main__":
    main()
