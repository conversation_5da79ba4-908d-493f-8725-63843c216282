import json
import os
import pandas as pd
from dotenv import load_dotenv
from openai import OpenAI
from openai.types.chat import ChatCompletionMessageParam
from typing import List, Dict, Any, Tuple
import numpy as np

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))


class LLMDataValidationAgent:
    """
    AI Agent that validates Excel file data row by row using OpenAI API
    based on validation rules from data_validation.json
    """
    
    def __init__(self, data_validation_path: str = None):
        # Default path relative to the Agent folder
        if data_validation_path is None:
            # Get the directory of this script
            script_dir = os.path.dirname(os.path.abspath(__file__))
            # Go up one level to the CBRE_Validation_Agent directory
            parent_dir = os.path.dirname(script_dir)
            data_validation_path = os.path.join(parent_dir, "store_procedure", "data_validation.json")

        self.data_validation_path = data_validation_path
        self.validation_rules = self._load_validation_rules()
        
    def _load_validation_rules(self) -> List[Dict[str, Any]]:
        """Load validation rules from data_validation.json"""
        try:
            with open(self.data_validation_path, "r") as f:
                rules = json.load(f)
            print(f"✅ Loaded {len(rules)} validation rules from {self.data_validation_path}")
            return rules
        except Exception as e:
            raise Exception(f"❌ Failed to load validation rules: {e}")
    
    def _create_validation_prompt(self, row_data: Dict[str, Any], row_index: int) -> str:
        """Create a prompt for LLM to validate a single row"""
        
        # Format validation rules for the prompt
        rules_text = ""
        for i, rule in enumerate(self.validation_rules, 1):
            rule_desc = rule.get("rule", "")
            rule_type = rule.get("type", "")
            columns = rule.get("columns", [])
            
            rules_text += f"{i}. {rule_desc}\n"
            if columns:
                rules_text += f"   - Applies to columns: {', '.join(columns)}\n"
            rules_text += f"   - Type: {rule_type}\n\n"
        
        # Format row data
        row_text = ""
        for column, value in row_data.items():
            row_text += f"- {column}: {value}\n"
        
        prompt = f"""
You are a data validation expert. Your job is to validate a single row of data against specific validation rules.

VALIDATION RULES:
{rules_text}

ROW DATA (Row #{row_index + 1}):
{row_text}

INSTRUCTIONS:
1. Check this row against ALL validation rules above
2. If the row passes all applicable rules, respond with: {{"is_correct": true, "why": "All validations passed"}}
3. If the row fails any rule, respond with: {{"is_correct": false, "why": "Specific reason for failure"}}
4. Be specific about which rule failed and why
5. Only return valid JSON - no explanations or markdown

RESPONSE FORMAT (JSON ONLY):
{{"is_correct": true/false, "why": "explanation"}}
"""
        return prompt
    
    def _validate_row_with_llm(self, row_data: Dict[str, Any], row_index: int) -> Tuple[bool, str]:
        """Validate a single row using OpenAI API"""
        try:
            prompt = self._create_validation_prompt(row_data, row_index)
            
            messages: List[ChatCompletionMessageParam] = [
                {"role": "user", "content": prompt}
            ]
            
            response = client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                temperature=0.1,
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            result = json.loads(content)
            
            is_correct = result.get("is_correct", False)
            why = result.get("why", "Unknown validation result")
            
            return is_correct, why
            
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON decode error for row {row_index + 1}: {e}")
            return False, f"LLM response parsing error: {e}"
        except Exception as e:
            print(f"⚠️ Validation error for row {row_index + 1}: {e}")
            return False, f"Validation error: {e}"
    
    def validate_file(self, file_path: str, output_path: str = None) -> pd.DataFrame:
        """
        Validate an Excel file and return DataFrame with validation results

        Args:
            file_path: Path to the Excel file to validate
            output_path: Optional path to save the validated file

        Returns:
            DataFrame with original data plus Is_correct and Why columns
        """
        try:
            # Handle relative paths - if file doesn't exist, try relative to parent directory
            if not os.path.exists(file_path):
                # Try relative to the parent directory (CBRE_Validation_Agent)
                script_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(script_dir)
                alternative_path = os.path.join(parent_dir, file_path)
                if os.path.exists(alternative_path):
                    file_path = alternative_path
                    print(f"📍 Using file path: {file_path}")

            # Read the Excel file
            print(f"📖 Reading file: {file_path}")
            df = pd.read_excel(file_path)
            print(f"✅ Loaded {len(df)} rows and {len(df.columns)} columns")
            
            # Initialize validation result columns
            df['Is_correct'] = False
            df['Why'] = ""
            
            # Validate each row
            print("🔍 Starting row-by-row validation...")
            for index, row in df.iterrows():
                print(f"⏳ Validating row {index + 1}/{len(df)}")
                
                # Convert row to dictionary, handling NaN values
                row_data = {}
                for col in df.columns:
                    if col not in ['Is_correct', 'Why']:  # Skip validation columns
                        value = row[col]
                        if pd.isna(value):
                            row_data[col] = None
                        else:
                            row_data[col] = value
                
                # Validate with LLM
                is_correct, why = self._validate_row_with_llm(row_data, index)
                
                # Update validation columns
                df.at[index, 'Is_correct'] = is_correct
                df.at[index, 'Why'] = why
                
                # Print progress
                status = "✅" if is_correct else "❌"
                print(f"   {status} Row {index + 1}: {why}")
            
            print(f"🎯 Validation complete!")
            
            # Save to file if output path provided
            if output_path:
                # Ensure output path is in the same directory as input file
                if not os.path.isabs(output_path):
                    input_dir = os.path.dirname(file_path)
                    output_path = os.path.join(input_dir, os.path.basename(output_path))
                self._save_validated_file(df, output_path)
            
            return df
            
        except Exception as e:
            raise Exception(f"❌ File validation failed: {e}")
    
    def _save_validated_file(self, df: pd.DataFrame, output_path: str):
        """Save the validated DataFrame to Excel file"""
        try:
            df.to_excel(output_path, index=False)
            print(f"💾 Validated file saved to: {output_path}")
        except Exception as e:
            print(f"⚠️ Failed to save file: {e}")
    
    def get_validation_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics of validation results"""
        total_rows = len(df)
        correct_rows = df['Is_correct'].sum()
        incorrect_rows = total_rows - correct_rows
        
        # Get error breakdown
        error_reasons = df[df['Is_correct'] == False]['Why'].value_counts().to_dict()
        
        summary = {
            "total_rows": total_rows,
            "correct_rows": int(correct_rows),
            "incorrect_rows": int(incorrect_rows),
            "success_rate": round((correct_rows / total_rows) * 100, 2) if total_rows > 0 else 0,
            "error_breakdown": error_reasons
        }
        
        return summary


def main():
    """Example usage of the LLM Data Validation Agent"""
    
    # Initialize the agent
    agent = LLMDataValidationAgent()
    
    # Example file path (user should provide this)
    input_file = input("Enter the path to your Excel file: data\FundHoldings_WithErrors.xlsx ").strip()

    # Handle relative paths - same logic as in validate_file
    original_path = input_file
    if not os.path.exists(input_file):
        # Try relative to the parent directory (CBRE_Validation_Agent)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(script_dir)
        alternative_path = os.path.join(parent_dir, input_file)
        if os.path.exists(alternative_path):
            input_file = alternative_path
            print(f"📍 Using resolved path: {input_file}")
        else:
            print(f"❌ File not found at: {original_path}")
            print(f"❌ Also tried: {alternative_path}")
            return
    
    # Generate output file name
    base_name = os.path.splitext(input_file)[0]
    output_file = f"{base_name}_validated.xlsx"
    
    try:
        # Validate the file
        validated_df = agent.validate_file(input_file, output_file)
        
        # Print summary
        summary = agent.get_validation_summary(validated_df)
        print("\n" + "="*50)
        print("📊 VALIDATION SUMMARY")
        print("="*50)
        print(f"Total rows: {summary['total_rows']}")
        print(f"Correct rows: {summary['correct_rows']}")
        print(f"Incorrect rows: {summary['incorrect_rows']}")
        print(f"Success rate: {summary['success_rate']}%")
        
        if summary['error_breakdown']:
            print("\n🔍 Error Breakdown:")
            for error, count in summary['error_breakdown'].items():
                print(f"  - {error}: {count} rows")
        
        print(f"\n💾 Validated file saved as: {output_file}")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")


if __name__ == "__main__":
    main()
