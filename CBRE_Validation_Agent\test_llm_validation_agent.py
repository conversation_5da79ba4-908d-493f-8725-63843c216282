import pandas as pd
import os
import sys
from Agent.llm_data_validation_agent import LLMDataValidationAgent


def create_test_data():
    """Create sample test data with both valid and invalid rows"""
    
    # Sample data with intentional validation issues
    test_data = {
        'UNIQUE_ID': [
            'FUND001',           # Valid
            'TOTAL_SUMMARY',     # Invalid - starts with 'TOTAL'
            'FUND003',           # Valid
            '',                  # Invalid - empty
            'FUND@05',           # Invalid - contains special character
            'FUND006'            # Valid
        ],
        'PORTFOLIO_ID': [
            'PORT001',
            'PORT002', 
            'PORT003',
            'PORT004',
            'PORT005',
            'PORT006'
        ],
        'REGISTERED_HOLDER': [
            'Holder A',
            'Holder B',
            'Holder C',
            'Holder D',
            'Holder E',
            'Holder F'
        ],
        'NAV': [
            1000.50,            # Valid
            0,                  # Invalid - zero
            2500.75,            # Valid
            None,               # Invalid - null
            -100,               # Invalid - negative
            1500.25             # Valid
        ],
        'OWNERSHIP_PERCENTAGE': [
            25.5,               # Valid
            0,                  # Invalid - zero
            15.75,              # Valid
            None,               # Invalid - null
            105.5,              # Invalid - over 100%
            30.25               # Valid
        ],
        'CAPITAL_CALLED': [
            500000,             # Valid
            0,                  # Invalid - zero
            750000,             # Valid
            None,               # Invalid - null
            250000,             # Valid
            600000              # Valid
        ],
        'NO_OF_SHARES': [
            1000,               # Valid
            0,                  # Invalid - zero
            1500,               # Valid
            None,               # Invalid - null
            2000,               # Valid
            1200                # Valid
        ],
        'COMMITTED_CAPITAL': [
            1000000,            # Valid
            0,                  # Invalid - zero
            1500000,            # Valid
            None,               # Invalid - null
            800000,             # Valid
            1200000             # Valid
        ],
        'PERIOD': [
            '2024-Q1',
            '2024-Q1',
            '2024-Q1',
            '2024-Q1',
            '2024-Q1',
            '2024-Q1'
        ],
        'FUND_NAME': [
            'Fund Alpha',
            'Fund Beta',
            'Fund Gamma',
            'Fund Delta',
            'Fund Epsilon',
            'Fund Zeta'
        ]
    }
    
    return pd.DataFrame(test_data)


def test_validation_agent():
    """Test the LLM Data Validation Agent"""
    
    print("🧪 Testing LLM Data Validation Agent")
    print("=" * 50)
    
    # Create test data
    print("📝 Creating test data...")
    test_df = create_test_data()
    
    # Save test data to Excel file
    test_file_path = "test_data_sample.xlsx"
    test_df.to_excel(test_file_path, index=False)
    print(f"✅ Test data saved to: {test_file_path}")
    
    # Display test data
    print("\n📊 Test Data Preview:")
    print(test_df.to_string(index=False))
    
    try:
        # Initialize the validation agent
        print("\n🤖 Initializing LLM Data Validation Agent...")
        agent = LLMDataValidationAgent()
        
        # Validate the test file
        print("\n🔍 Starting validation process...")
        output_file = "test_data_sample_validated.xlsx"
        validated_df = agent.validate_file(test_file_path, output_file)
        
        # Display validation results
        print("\n📋 Validation Results:")
        print("=" * 80)
        
        # Show only relevant columns for readability
        result_columns = ['UNIQUE_ID', 'NAV', 'OWNERSHIP_PERCENTAGE', 'Is_correct', 'Why']
        if all(col in validated_df.columns for col in result_columns):
            print(validated_df[result_columns].to_string(index=False))
        else:
            print(validated_df.to_string(index=False))
        
        # Get and display summary
        summary = agent.get_validation_summary(validated_df)
        print("\n📊 VALIDATION SUMMARY")
        print("=" * 50)
        print(f"Total rows: {summary['total_rows']}")
        print(f"Correct rows: {summary['correct_rows']}")
        print(f"Incorrect rows: {summary['incorrect_rows']}")
        print(f"Success rate: {summary['success_rate']}%")
        
        if summary['error_breakdown']:
            print("\n🔍 Error Breakdown:")
            for error, count in summary['error_breakdown'].items():
                print(f"  - {error}: {count} rows")
        
        print(f"\n💾 Validated file saved as: {output_file}")
        
        # Analyze expected vs actual results
        print("\n🎯 Test Analysis:")
        print("=" * 50)
        
        expected_issues = [
            "Row 2: UNIQUE_ID starts with 'TOTAL'",
            "Row 4: UNIQUE_ID is empty", 
            "Row 5: UNIQUE_ID contains special characters",
            "Rows with zero/null financial values"
        ]
        
        print("Expected validation issues:")
        for issue in expected_issues:
            print(f"  ✓ {issue}")
        
        # Check if validation caught the expected issues
        incorrect_rows = validated_df[validated_df['Is_correct'] == False]
        print(f"\nActual issues found: {len(incorrect_rows)} rows")
        
        if len(incorrect_rows) > 0:
            print("Issues detected:")
            for idx, row in incorrect_rows.iterrows():
                print(f"  - Row {idx + 1}: {row['Why']}")
        
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        # Clean up test files
        try:
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
                print(f"🧹 Cleaned up: {test_file_path}")
        except:
            pass
    
    return True


def test_with_custom_file():
    """Test with a user-provided file"""
    
    print("\n🔧 Testing with custom file")
    print("=" * 50)
    
    file_path = input("Enter path to your Excel file (or press Enter to skip): ").strip()
    
    if not file_path:
        print("⏭️ Skipping custom file test")
        return
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    try:
        agent = LLMDataValidationAgent()
        
        # Generate output file name
        base_name = os.path.splitext(file_path)[0]
        output_file = f"{base_name}_validated.xlsx"
        
        # Validate the file
        validated_df = agent.validate_file(file_path, output_file)
        
        # Show summary
        summary = agent.get_validation_summary(validated_df)
        print("\n📊 VALIDATION SUMMARY")
        print("=" * 50)
        print(f"Total rows: {summary['total_rows']}")
        print(f"Correct rows: {summary['correct_rows']}")
        print(f"Incorrect rows: {summary['incorrect_rows']}")
        print(f"Success rate: {summary['success_rate']}%")
        
        print(f"\n💾 Validated file saved as: {output_file}")
        
    except Exception as e:
        print(f"❌ Custom file test failed: {e}")


def main():
    """Main test function"""
    
    print("🚀 LLM Data Validation Agent Test Suite")
    print("=" * 60)
    
    # Check if data_validation.json exists
    if not os.path.exists("store_procedure/data_validation.json"):
        print("❌ data_validation.json not found!")
        print("Please run extract_rules_agent.py first to generate validation rules.")
        return
    
    # Run automated test
    success = test_validation_agent()
    
    if success:
        # Optionally test with custom file
        test_with_custom_file()
    
    print("\n🎉 Test suite completed!")


if __name__ == "__main__":
    main()
