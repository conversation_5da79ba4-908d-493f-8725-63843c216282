"""
Debug test to identify issues with the LLM Data Validation Agent
"""

import os
import pandas as pd
from llm_data_validation_agent import LLMDataValidationAgent


def test_step_by_step():
    print("🔍 Debug Test - Step by Step")
    print("=" * 50)
    
    try:
        # Step 1: Test agent initialization
        print("1️⃣ Testing agent initialization...")
        agent = LLMDataValidationAgent()
        print(f"✅ Agent initialized with {len(agent.validation_rules)} rules")
        
        # Step 2: Test file path resolution
        print("\n2️⃣ Testing file path resolution...")
        file_path = "data/FundHoldings_WithErrors.xlsx"
        print(f"Original path: {file_path}")
        
        if not os.path.exists(file_path):
            script_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(script_dir)
            alternative_path = os.path.join(parent_dir, file_path)
            if os.path.exists(alternative_path):
                file_path = alternative_path
                print(f"✅ Using resolved path: {file_path}")
            else:
                print(f"❌ File not found at: {alternative_path}")
                return
        
        # Step 3: Test file reading
        print("\n3️⃣ Testing file reading...")
        df = pd.read_excel(file_path)
        print(f"✅ File loaded: {len(df)} rows, {len(df.columns)} columns")
        print(f"Columns: {list(df.columns)}")
        
        # Step 4: Test with just one row
        print("\n4️⃣ Testing validation with first row only...")
        first_row = df.iloc[0]
        
        # Convert to dict
        row_data = {}
        for col in df.columns:
            value = first_row[col]
            if pd.isna(value):
                row_data[col] = None
            else:
                row_data[col] = value
        
        print(f"First row data: {row_data}")
        
        # Test validation prompt creation
        print("\n5️⃣ Testing validation prompt creation...")
        prompt = agent._create_validation_prompt(row_data, 0)
        print("✅ Prompt created successfully")
        print(f"Prompt length: {len(prompt)} characters")
        
        # Test OpenAI API call
        print("\n6️⃣ Testing OpenAI API call...")
        try:
            is_correct, why = agent._validate_row_with_llm(row_data, 0)
            print(f"✅ API call successful!")
            print(f"Result: is_correct={is_correct}, why='{why}'")
        except Exception as e:
            print(f"❌ API call failed: {e}")
            return
        
        # Step 7: Test full validation with small dataset
        print("\n7️⃣ Testing full validation with first 2 rows...")
        small_df = df.head(2).copy()
        
        # Create a temporary file
        temp_file = "temp_test.xlsx"
        small_df.to_excel(temp_file, index=False)
        
        # Validate
        output_file = "temp_test_validated.xlsx"
        validated_df = agent.validate_file(temp_file, output_file)
        
        print("✅ Validation completed!")
        print("\nResults:")
        print(validated_df[['UNIQUE_ID', 'Is_correct', 'Why']].to_string(index=False))
        
        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)
        if os.path.exists(output_file):
            print(f"✅ Output file created: {output_file}")
            os.remove(output_file)
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed at step: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_step_by_step()
