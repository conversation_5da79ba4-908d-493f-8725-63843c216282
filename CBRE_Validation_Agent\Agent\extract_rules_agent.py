import json
import os
from dotenv import load_dotenv
from openai import OpenAI
from openai.types.chat import ChatCompletionMessageParam
from typing import List, Dict, Any  

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def extract_validation_parts(procedure_text: str, column_template: dict, procedure_name: str) -> dict:
    prompt = f"""
You are a data validation expert.

A column template and a SQL stored procedure are provided to you.

Your job is to extract **schema validation rules** and **data validation rules** from the procedure, using the context of the column template.

If column names are not explicitly mentioned in the procedure, try to **infer them**. If they truly cannot be determined, use an **empty list** for the `"columns"` field.

You are also allowed to define abstract rules like `"Column count in uploaded file should match template"` or `"Missing required columns"`.

⚠️ Do NOT return any natural language or explanation. Just return a valid JSON response in the structure below.

----------------------
📁 COLUMN TEMPLATE
Required Columns:
{json.dumps(column_template['required_columns'], indent=2)}

Data Types:
{json.dumps(column_template['data_types'], indent=2)}

----------------------
🧾 STORED PROCEDURE
{procedure_text}

----------------------
✅ OUTPUT FORMAT (JSON ONLY):
{{
  "schema_validation": [
    {{
      "rule": "description of the schema validation rule",
      "source_procedure": "{procedure_name}",
      "columns": [...],
      "type": "column_presence_check / column_count_check / datatype_check"
    }}
  ],
  "data_validation": [
    {{
      "rule": "description of the data validation rule",
      "source_procedure": "{procedure_name}",
      "columns": [...],
      "type": "null_check / value_range / format_check / duplicate_check / business_rule"
    }}
  ]
}}
"""
    try:
        messages: list[ChatCompletionMessageParam] = [{"role": "user", "content": prompt}]
        response = client.chat.completions.create(
            model="gpt-4",
            messages=messages,
            temperature=0.2
        )
        content = response.choices[0].message.content
        return json.loads(content)

    except Exception as e:
        print(f"❌ Error: {e}")
        return {"schema_validation": [], "data_validation": []}


def fallback_injection(procedure_name: str, procedure_text: str) -> List[Dict[str, Any]]:
    """Inject fallback rules if LLM gives no output for schema-related structural checks"""
    fallback_rules = []

    if procedure_name == "SP_VALIDATE_FILE_STRUCTURE":
        fallback_rules.append({
            "rule": "Validate that the number of columns in uploaded file matches the expected template",
            "source_procedure": procedure_name,
            "columns": [],
            "type": "column_count_check"
        })

    if procedure_name == "SP_VALIDATE_MANDATORY_COLUMNS":
        fallback_rules.append({
            "rule": "Ensure all required columns defined in template exist in the uploaded file",
            "source_procedure": procedure_name,
            "columns": [],
            "type": "column_presence_check"
        })

    return fallback_rules


def main():
    with open("store_procedure/validation_rules_updated.json", "r") as f:
        procedures = json.load(f)

    with open("schema_templates/column_template.json", "r") as f:
        column_template = json.load(f)

    all_schema_rules = []
    all_data_rules = []

    for proc in procedures:
        print(f"⏳ Processing: {proc['procedure_name']}...")
        result = extract_validation_parts(proc["procedure_definition"], column_template, proc["procedure_name"])

        # If LLM gives nothing, use fallback
        if not result["schema_validation"] and not result["data_validation"]:
            print(f"⚠️  LLM returned empty for {proc['procedure_name']}. Using fallback rules.")
            fallback = fallback_injection(proc["procedure_name"], proc["procedure_definition"])
            all_schema_rules.extend(fallback)
            continue

        for rule in result.get("schema_validation", []):
            all_schema_rules.append(rule)

        for rule in result.get("data_validation", []):
            all_data_rules.append(rule)

    os.makedirs("store_procedure", exist_ok=True)

    with open("store_procedure/schema_validation.json", "w") as f:
        json.dump(all_schema_rules, f, indent=2)

    with open("store_procedure/data_validation.json", "w") as f:
        json.dump(all_data_rules, f, indent=2)

    print("✅ schema_validation.json and data_validation.json have been generated.")


if __name__ == "__main__":
    main()
