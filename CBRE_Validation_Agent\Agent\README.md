# LLM Data Validation Agent

An AI-powered data validation agent that validates Excel files row by row using OpenAI's GPT-4 model based on validation rules extracted from stored procedures.

## Overview

The LLM Data Validation Agent is part of the CBRE Validation Agent system. It works in conjunction with the `extract_rules_agent.py` to provide intelligent data validation:

1. **extract_rules_agent.py** → Reads stored procedures → Creates `data_validation.json` and `schema_validation.json`
2. **llm_data_validation_agent.py** → Reads `data_validation.json` → Validates Excel files using AI

## Features

- 🤖 **AI-Powered Validation**: Uses OpenAI GPT-4 to understand and apply complex validation rules
- 📊 **Row-by-Row Analysis**: Validates each row individually with detailed feedback
- 📋 **Comprehensive Output**: Original data + `Is_correct` + `Why` columns
- 📈 **Validation Summary**: Success rates and error breakdowns
- 🔧 **Flexible Rules**: Supports various validation types (null_check, format_check, business_rule, etc.)

## Prerequisites

1. **Environment Setup**:
   ```bash
   pip install pandas openpyxl python-dotenv openai
   ```

2. **OpenAI API Key**:
   Create a `.env` file in the project root:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

3. **Validation Rules**:
   Run `extract_rules_agent.py` first to generate `data_validation.json`:
   ```bash
   python extract_rules_agent.py
   ```

## Usage

### Method 1: Direct Script Execution

```bash
# Interactive mode
python llm_data_validation_agent.py

# Command line with file path
python example_usage.py path/to/your/file.xlsx
```

### Method 2: Import as Module

```python
from Agent.llm_data_validation_agent import LLMDataValidationAgent

# Initialize the agent
agent = LLMDataValidationAgent()

# Validate a file
validated_df = agent.validate_file("input.xlsx", "output_validated.xlsx")

# Get summary
summary = agent.get_validation_summary(validated_df)
print(f"Success rate: {summary['success_rate']}%")
```

### Method 3: Testing

```bash
# Run comprehensive tests
python test_llm_validation_agent.py
```

## How It Works

### 1. Rule Loading
The agent loads validation rules from `data_validation.json`:

```json
[
  {
    "rule": "UNIQUE_ID should not start with 'TOTAL'",
    "source_procedure": "SP_CLEAN_DATA",
    "columns": ["UNIQUE_ID"],
    "type": "format_check"
  },
  {
    "rule": "NAV must be numeric and greater than zero",
    "columns": ["NAV"],
    "type": "null_check"
  }
]
```

### 2. Row Validation
For each row, the agent:
1. Converts row data to a dictionary
2. Creates a validation prompt with all rules
3. Sends to OpenAI GPT-4 for analysis
4. Receives JSON response: `{"is_correct": true/false, "why": "explanation"}`

### 3. Output Generation
Creates a new Excel file with:
- All original columns
- `Is_correct`: Boolean validation result
- `Why`: Detailed explanation of validation outcome

## Validation Rules Supported

The agent understands various validation rule types:

- **null_check**: Validates required fields and non-null constraints
- **format_check**: Validates data formats and patterns
- **duplicate_check**: Identifies duplicate values
- **business_rule**: Applies complex business logic
- **value_range**: Validates numeric ranges
- **datatype_check**: Validates data types

## Example Output

| UNIQUE_ID | NAV | OWNERSHIP_PERCENTAGE | Is_correct | Why |
|-----------|-----|---------------------|------------|-----|
| FUND001 | 1000.50 | 25.5 | true | All validations passed |
| TOTAL_SUMMARY | 0 | 0 | false | UNIQUE_ID cannot start with 'TOTAL' |
| FUND003 | 2500.75 | 15.75 | true | All validations passed |
| | | | false | UNIQUE_ID should not be null or empty |

## Configuration

### Custom Validation Rules Path
```python
agent = LLMDataValidationAgent(
    data_validation_path="custom/path/to/validation_rules.json"
)
```

### OpenAI Model Settings
The agent uses GPT-4 with:
- Temperature: 0.1 (for consistent results)
- Max tokens: 500
- JSON response format

## Error Handling

The agent includes robust error handling:
- JSON parsing errors from LLM responses
- File reading/writing errors
- API connection issues
- Invalid data formats

## Performance Considerations

- **API Costs**: Each row requires one OpenAI API call
- **Processing Time**: Depends on file size and API response time
- **Rate Limits**: Respects OpenAI API rate limits

## Files Structure

```
Agent/
├── llm_data_validation_agent.py    # Main validation agent
├── example_usage.py                # Usage examples
└── README.md                       # This documentation

../
├── test_llm_validation_agent.py    # Test suite
└── store_procedure/
    └── data_validation.json         # Validation rules (generated)
```

## Troubleshooting

### Common Issues

1. **"Validation rules not found"**
   - Run `extract_rules_agent.py` first
   - Check if `store_procedure/data_validation.json` exists

2. **"OpenAI API error"**
   - Verify your API key in `.env` file
   - Check your OpenAI account credits
   - Ensure internet connectivity

3. **"File not found"**
   - Verify the Excel file path
   - Ensure file has `.xlsx` or `.xls` extension

4. **"JSON parsing error"**
   - LLM response was not valid JSON
   - Check the validation prompt format
   - Retry the validation

## Contributing

To extend the validation agent:

1. Add new validation rule types in the prompt template
2. Enhance error handling for specific scenarios
3. Add support for additional file formats
4. Implement batch processing for large files

## License

This project is part of the CBRE Validation Agent system.
