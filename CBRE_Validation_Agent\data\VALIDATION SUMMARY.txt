==================================================
📊 VALIDATION SUMMARY
==================================================
Total rows: 100
Correct rows: 73
Incorrect rows: 27
Success rate: 73.0%

🔍 Error Breakdown:
  - The NAV field is null which violates the specific validation rule 1 and basic completeness validation rule 8: 3 rows
  - OWNERSHIP_PERCENTAGE is null which violates the specific validation rule 1 and basic completeness validation rule 8: 3 rows
  - UNIQUE_ID is null or empty which violates the specific validation rule 5 and basic completeness validation. Also, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL are null or zero which violates the specific validation rule 1 and basic completeness validation.: 2 rows
  - UNIQUE_ID is null or empty which fails the specific validation rule 5 and basic completeness validation rule 8: 2 rows
  - The field OWNERSHIP_PERCENTAGE is null which violates the specific validation rule 1 and basic completeness validation rule 8: 2 rows
  - UNIQUE_ID is null or empty which violates the specific validation rule 5 and basic completeness validation: 1 rows
  - UNIQUE_ID 'UID_4671' is duplicated. It appears in rows: [48, 93]: 1 rows
  - UNIQUE_ID 'UID_1842' is duplicated. It appears in rows: [32, 90]: 1 rows
  - UNIQUE_ID is null or empty which fails the specific validation rule 5 and basic completeness validation rule: 1 rows
  - The NAV field is null which violates the specific validation rule 1 and basic completeness validation rule 8. Also, the UNIQUE_ID 'UID_3216' is duplicated which violates the specific validation rule 6.: 1 rows
  - UNIQUE_ID is null or empty which violates the specific validation rule 5 and basic completeness validation rule: 1 rows
  - UNIQUE_ID 'UID_3553' is duplicated in rows 43 and 63: 1 rows
  - UNIQUE_ID 'UID_8637' is duplicated. It appears in rows: [1, 31]. Rule: UNIQUE_ID should not have duplicates: 1 rows
  - The NAV field is null, which violates the specific validation rule 1 and the basic completeness validation rule 8.: 1 rows
  - Rule 1 failed: COMMITTED_CAPITAL is less than zero which is not allowed: 1 rows
  - UNIQUE_ID 'UID_2350' is duplicated in rows 40 and 96: 1 rows
  - UNIQUE_ID 'UID_1842' is duplicated in rows 32 and 90: 1 rows
  - UNIQUE_ID 'UID_8637' is duplicated. It appears in rows: [1, 31]: 1 rows
  - UNIQUE_ID is null or empty which fails the specific validation rule 5 and basic completeness validation. Also, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL are null or zero which fails the specific validation rule 1 and basic completeness validation.: 1 rows
  - UNIQUE_ID 'UID_2350' is duplicated. It appears in rows: [40, 96]. Rule violated: UNIQUE_ID should not have duplicates: 1 rows

💾 Validated file saved as: C:\Users\<USER>\OneDrive\Desktop\PythonDataScience\Office\CBRE-AGENT\CBRE_Validation_Agent\data\FundHoldings_WithErrors_validated.xlsx
