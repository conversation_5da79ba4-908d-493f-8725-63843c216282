import pandas as pd
import json
import re
import os
from typing import Dict, Any, List, Tuple, Optional
from core.llm_config import get_llm


class DataValidationAgent:
    def __init__(self, llm=None):
        self.llm = llm or get_llm()
        self.validation_rules_path = "store_procedure/validation_rules_updated.json"
        self.enhanced_rules_path = "store_procedure/enhanced_validation_rules.json"
        self.schema_template_path = "schema_templates/column_template.json"
        self.schema_rules = self._load_schema_rules()
        self.business_rules = self._load_business_rules()
        self.file_type = "FundHoldings"

    def validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        result_df = df.copy()
        result_df['Is_correct'] = True
        result_df['Why'] = ''

        unique_ids = []  
        for index, row in result_df.iterrows():
            is_correct, error_messages = self._validate_row(row, index)
            result_df.at[index, 'Is_correct'] = is_correct
            result_df.at[index, 'Why'] = "; ".join(error_messages) if error_messages else ""

            if 'UNIQUE_ID' in row and pd.notna(row['UNIQUE_ID']):
                unique_ids.append((index, str(row['UNIQUE_ID']).strip()))

        self._validate_duplicates(result_df, unique_ids)

        return result_df

    def _load_schema_rules(self) -> Dict[str, Any]:
        try:
            if os.path.exists(self.enhanced_rules_path):
                with open(self.enhanced_rules_path, "r") as f:
                    enhanced_rules = json.load(f)
                    return enhanced_rules.get("schema_definition", {})
            with open(self.schema_template_path, "r") as f:
                template = json.load(f)
                return self._convert_template_to_schema(template)
        except Exception as e:
            raise Exception(f"Failed to load schema rules: {e}")

    def _load_business_rules(self) -> List[Dict[str, Any]]:
        try:
            if os.path.exists(self.enhanced_rules_path):
                with open(self.enhanced_rules_path, "r") as f:
                    enhanced_rules = json.load(f)
                    return enhanced_rules.get("business_rules", [])
            return []
        except Exception as e:
            print(f"Warning: Could not load business rules: {e}")
            return []

    def _convert_template_to_schema(self, template: Dict[str, Any]) -> Dict[str, Any]:
        columns = []
        for i, column_name in enumerate(template.get("required_columns", []), 1):
            data_type = template.get("data_types", {}).get(column_name, "string")
            columns.append({
                "column_name": column_name,
                "data_type": "STRING" if data_type == "string" else "NUMBER",
                "is_required": True,
                "nullable": False,
                "position": i,
                "validation_rules": []
            })
        return {"columns": columns}

    def _validate_row(self, row: pd.Series, row_index: int) -> Tuple[bool, List[str]]:
        errors = []
        for column_def in self.schema_rules.get("columns", []):
            errors.extend(self._validate_field(row, column_def, row_index))
        errors.extend(self._validate_business_rules(row, row_index))
        return len(errors) == 0, errors

    def _validate_field(self, row: pd.Series, column_def: Dict[str, Any], row_index: int) -> List[str]:
        errors = []
        column_name = column_def["column_name"]

        if column_name not in row:
            if column_def.get("is_required", False):
                errors.append(f"Required field '{column_name}' is missing")
            return errors

        value = row[column_name]

        if not column_def.get("nullable", True):
            if pd.isna(value) or (isinstance(value, str) and str(value).strip() == ''):
                errors.append(f"Field '{column_name}' cannot be null or empty")
                return errors

        if pd.notna(value):
            data_type = column_def.get("data_type", "STRING")
            errors.extend(self._validate_data_type(column_name, value, data_type))
            errors.extend(self._validate_constraints(column_name, value, column_def))
            validation_rules = column_def.get("validation_rules", [])
            errors.extend(self._apply_validation_rules(column_name, value, validation_rules))

        return errors

    def _validate_data_type(self, column_name: str, value: Any, data_type: str) -> List[str]:
        errors = []
        if data_type == "STRING":
            try:
                str(value)
            except:
                errors.append(f"Field '{column_name}' must be a valid string")
        elif data_type == "NUMBER":
            try:
                float(value)
            except (ValueError, TypeError):
                errors.append(f"Field '{column_name}' must be numeric, found: '{value}'")
        return errors

    def _validate_constraints(self, column_name: str, value: Any, column_def: Dict[str, Any]) -> List[str]:
        errors = []
        if column_def.get("data_type") == "STRING" and isinstance(value, str):
            min_length = column_def.get("min_length")
            max_length = column_def.get("max_length")
            if min_length is not None and len(value) < min_length:
                errors.append(f"Field '{column_name}' must be at least {min_length} characters")
            if max_length is not None and len(value) > max_length:
                errors.append(f"Field '{column_name}' must be at most {max_length} characters")

        if column_def.get("data_type") == "NUMBER":
            try:
                num_value = float(value)
                min_value = column_def.get("min_value")
                max_value = column_def.get("max_value")
                if min_value is not None and num_value < min_value:
                    errors.append(f"Field '{column_name}' must be at least {min_value}")
                if max_value is not None and num_value > max_value:
                    errors.append(f"Field '{column_name}' must be at most {max_value}")
                decimal_places = column_def.get("decimal_places")
                if decimal_places is not None:
                    if round(num_value, decimal_places) != num_value:
                        errors.append(f"Field '{column_name}' must have at most {decimal_places} decimal places")
            except (ValueError, TypeError):
                pass

        regex_pattern = column_def.get("regex_pattern")
        if regex_pattern and isinstance(value, str):
            if not re.match(regex_pattern, value):
                errors.append(f"Field '{column_name}' does not match required format")
        return errors

    def _apply_validation_rules(self, column_name: str, value: Any, validation_rules: List[Dict[str, Any]]) -> List[str]:
        errors = []
        for rule in validation_rules:
            rule_type = rule.get("rule_type")
            if rule_type == "not_starts_with":
                if isinstance(value, str) and value.upper().startswith(rule.get("value", "").upper()):
                    errors.append(rule.get("error_message", f"Field '{column_name}' validation failed"))
            elif rule_type == "regex_clean":
                if isinstance(value, str):
                    pattern = rule.get("pattern", "")
                    replacement = rule.get("replacement", "")
                    re.sub(pattern, replacement, value)
        return errors

    def _validate_business_rules(self, row: pd.Series, row_index: int) -> List[str]:
        errors = []
        if 'UNIQUE_ID' in row and pd.notna(row['UNIQUE_ID']):
            unique_id = str(row['UNIQUE_ID']).strip().upper()
            if unique_id.startswith('TOTAL'):
                errors.append("UNIQUE_ID cannot start with 'TOTAL'")

        financial_fields = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']
        all_financial_zero_or_null = True
        for field in financial_fields:
            if field in row and pd.notna(row[field]):
                try:
                    if float(row[field]) != 0:
                        all_financial_zero_or_null = False
                        break
                except (ValueError, TypeError):
                    pass
        if all_financial_zero_or_null:
            errors.append("All financial fields cannot be zero or null simultaneously")

        for business_rule in self.business_rules:
            rule_type = business_rule.get("rule_type")
            if rule_type == "not_all_zero_or_null":
                fields = business_rule.get("fields", [])
                all_zero_null = all(
                    field not in row or pd.isna(row[field]) or
                    (pd.notna(row[field]) and float(row[field]) == 0)
                    for field in fields if field in row
                )
                if all_zero_null:
                    errors.append(business_rule.get("error_message", "Business rule validation failed"))
        return errors

    def _validate_duplicates(self, result_df: pd.DataFrame, unique_ids: List[Tuple[int, str]]) -> None:
        seen_ids = {}
        for row_index, unique_id in unique_ids:
            if unique_id in seen_ids:
                error_msg = f"Duplicate UNIQUE_ID '{unique_id}' found"
                current_error = result_df.at[row_index, 'Why']
                result_df.at[row_index, 'Why'] = f"{current_error}; {error_msg}" if current_error else error_msg
                result_df.at[row_index, 'Is_correct'] = False
                prev_row_index = seen_ids[unique_id]
                prev_error = result_df.at[prev_row_index, 'Why']
                result_df.at[prev_row_index, 'Why'] = f"{prev_error}; {error_msg}" if prev_error else error_msg
                result_df.at[prev_row_index, 'Is_correct'] = False
            else:
                seen_ids[unique_id] = row_index