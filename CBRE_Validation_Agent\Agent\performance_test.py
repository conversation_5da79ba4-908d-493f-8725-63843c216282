"""
Performance test to compare sequential vs parallel validation
"""

import time
import pandas as pd
from llm_data_validation_agent import LLMDataValidationAgent


def create_test_data(num_rows=10):
    """Create test data with specified number of rows"""
    
    base_data = {
        'UNIQUE_ID': 'FUND',
        'PORTFOLIO_ID': 'PORT',
        'REGISTERED_HOLDER': 'Holder',
        'NAV': 1000.50,
        'OWNERSHIP_PERCENTAGE': 25.5,
        'CAPITAL_CALLED': 500000,
        'NO_OF_SHARES': 1000,
        'COMMITTED_CAPITAL': 1000000,
        'PERIOD': '2024-Q1',
        'FUND_NAME': 'Fund Alpha'
    }
    
    # Create multiple rows with variations
    test_data = {}
    for col in base_data.keys():
        test_data[col] = []
    
    for i in range(num_rows):
        for col, base_value in base_data.items():
            if col in ['UNIQUE_ID', 'PORTFOLIO_ID']:
                test_data[col].append(f"{base_value}{i+1:03d}")
            elif col == 'FUND_NAME':
                test_data[col].append(f"{base_value} {i+1}")
            elif col == 'REGISTERED_HOLDER':
                test_data[col].append(f"{base_value} {chr(65+i%26)}")
            else:
                test_data[col].append(base_value)
    
    # Add some intentional errors
    if num_rows > 2:
        test_data['UNIQUE_ID'][1] = 'TOTAL_SUMMARY'  # Should fail
        test_data['NAV'][2] = 0  # Should fail
    
    return pd.DataFrame(test_data)


def run_performance_test():
    """Run performance comparison between sequential and parallel processing"""
    
    print("🏃‍♂️ Performance Test - Sequential vs Parallel Validation")
    print("=" * 60)
    
    # Test parameters
    test_sizes = [5, 10]  # Start with smaller sizes for testing
    max_workers_options = [3, 5]
    
    results = []
    
    for num_rows in test_sizes:
        print(f"\n📊 Testing with {num_rows} rows")
        print("-" * 40)
        
        # Create test data
        test_df = create_test_data(num_rows)
        test_file = f"perf_test_{num_rows}_rows.xlsx"
        test_df.to_excel(test_file, index=False)
        
        try:
            # Test Sequential Processing
            print("🐌 Testing Sequential Processing...")
            agent_seq = LLMDataValidationAgent(max_workers=1)
            
            start_time = time.time()
            validated_df_seq = agent_seq.validate_file(test_file, use_parallel=False)
            seq_time = time.time() - start_time
            
            print(f"   ⏱️ Sequential time: {seq_time:.2f} seconds")
            
            # Test Parallel Processing
            for workers in max_workers_options:
                print(f"🚀 Testing Parallel Processing ({workers} workers)...")
                agent_par = LLMDataValidationAgent(max_workers=workers)
                
                start_time = time.time()
                validated_df_par = agent_par.validate_file(test_file, use_parallel=True)
                par_time = time.time() - start_time
                
                speedup = seq_time / par_time if par_time > 0 else 0
                print(f"   ⏱️ Parallel time ({workers} workers): {par_time:.2f} seconds")
                print(f"   🚀 Speedup: {speedup:.2f}x")
                
                results.append({
                    'rows': num_rows,
                    'workers': workers,
                    'sequential_time': seq_time,
                    'parallel_time': par_time,
                    'speedup': speedup
                })
        
        except Exception as e:
            print(f"❌ Error testing {num_rows} rows: {e}")
        
        finally:
            # Clean up
            import os
            if os.path.exists(test_file):
                os.remove(test_file)
    
    # Summary
    print("\n📈 Performance Summary")
    print("=" * 60)
    print(f"{'Rows':<6} {'Workers':<8} {'Sequential':<12} {'Parallel':<10} {'Speedup':<8}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['rows']:<6} {result['workers']:<8} {result['sequential_time']:<12.2f} "
              f"{result['parallel_time']:<10.2f} {result['speedup']:<8.2f}x")
    
    # Recommendations
    print("\n💡 Recommendations:")
    if results:
        best_result = max(results, key=lambda x: x['speedup'])
        print(f"   • Best performance: {best_result['workers']} workers with {best_result['speedup']:.2f}x speedup")
        print(f"   • For {best_result['rows']} rows: {best_result['parallel_time']:.2f}s vs {best_result['sequential_time']:.2f}s")
    
    print("   • Parallel processing is most beneficial for larger datasets")
    print("   • Consider network latency and API rate limits when choosing worker count")
    print("   • Start with 3-5 workers for optimal balance of speed and resource usage")


def quick_performance_test():
    """Quick test with just a few rows"""
    
    print("⚡ Quick Performance Test")
    print("=" * 30)
    
    # Create small test dataset
    test_df = create_test_data(3)
    test_file = "quick_perf_test.xlsx"
    test_df.to_excel(test_file, index=False)
    
    try:
        agent = LLMDataValidationAgent(max_workers=3)
        
        # Sequential
        print("🐌 Sequential (3 rows)...")
        start = time.time()
        agent.validate_file(test_file, use_parallel=False)
        seq_time = time.time() - start
        
        # Parallel
        print("🚀 Parallel (3 rows)...")
        start = time.time()
        agent.validate_file(test_file, use_parallel=True)
        par_time = time.time() - start
        
        speedup = seq_time / par_time if par_time > 0 else 0
        print(f"\n📊 Results:")
        print(f"   Sequential: {seq_time:.2f}s")
        print(f"   Parallel:   {par_time:.2f}s")
        print(f"   Speedup:    {speedup:.2f}x")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        import os
        if os.path.exists(test_file):
            os.remove(test_file)


def main():
    """Main function"""
    
    print("🧪 LLM Data Validation Agent - Performance Testing")
    print("=" * 60)
    
    choice = input("\nChoose test type:\n1. Quick test (3 rows)\n2. Full performance test\nEnter choice (1-2): ").strip()
    
    if choice == "1":
        quick_performance_test()
    elif choice == "2":
        run_performance_test()
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    main()
